# Override para desenvolvimento
services:
  wplace-proxy:
    # Para desenvolvimento, usar volumes para hot reload
    volumes:
      - ./src:/usr/src/app/src
      - ./artworks:/usr/src/app/artworks
      - ./tiles:/usr/src/app/tiles
    # Comando para desenvolvimento com watch mode
    command: ["bun", "run", "--watch", "src/main.ts"]
    environment:
      - NODE_ENV=development
    
  # Adicionar serviços de desenvolvimento
  redis-commander:
    profiles: []  # Remove o profile para sempre executar em dev
