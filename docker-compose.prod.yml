# Configuração para produção
# Use: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
services:
  wplace-proxy:
    restart: always
    environment:
      - NODE_ENV=production
    # Remover volumes de desenvolvimento
    volumes:
      - ./artworks:/usr/src/app/artworks
      - ./tiles:/usr/src/app/tiles
    # Usar comando padrão do Dockerfile
    command: ["bun", "run", "src/main.ts"]
    
  nginx:
    profiles: []  # Remove o profile para sempre executar em prod
    
  redis:
    # Configurações de produção para Redis
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
      
  # Remover serviços de desenvolvimento
  redis-commander:
    profiles:
      - never  # Nunca executar em produção
