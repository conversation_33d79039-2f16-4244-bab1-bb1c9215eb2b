# Configuração simplificada do Redis para o wplace-proxy

# Configurações básicas
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 60

# Configurações de memória
maxmemory 512mb
maxmemory-policy allkeys-lru

# Configurações de persistência (mais permissivas)
save 900 1
save 300 10
save 60 10000

# Configurações de log
loglevel notice
logfile ""

# Configurações de performance (mais permissivas)
tcp-backlog 511
databases 16

# Configurações de timeout mais permissivas
tcp-user-timeout 30000

# Configurações para JSON
# O Redis Stack já inclui o módulo RedisJSON automaticamente
