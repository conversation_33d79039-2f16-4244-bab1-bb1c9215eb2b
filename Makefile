# Makefile para facilitar o gerenciamento do projeto

.PHONY: help build up down logs clean dev prod restart

# Configurações
COMPOSE_FILE = docker-compose.yml
COMPOSE_FILE_PROD = docker-compose.yml -f docker-compose.prod.yml

help: ## Mostra esta ajuda
	@echo "Comandos disponíveis:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

build: ## Constrói as imagens Docker
	docker-compose build

up: ## Inicia os serviços em modo desenvolvimento
	docker-compose up -d

down: ## Para todos os serviços
	docker-compose down

logs: ## Mostra os logs dos serviços
	docker-compose logs -f

clean: ## Remove containers, volumes e imagens não utilizados
	docker-compose down -v --remove-orphans
	docker system prune -f

dev: ## Inicia em modo desenvolvimento com logs
	docker-compose up --build

prod: ## Inicia em modo produção
	docker-compose -f $(COMPOSE_FILE_PROD) up -d --build

restart: ## Reinicia todos os serviços
	docker-compose restart

# Comandos específicos
redis-cli: ## Conecta ao Redis CLI
	docker-compose exec redis redis-cli

app-shell: ## Acessa o shell do container da aplicação
	docker-compose exec wplace-proxy sh

backup-redis: ## Faz backup do Redis
	docker-compose exec redis redis-cli BGSAVE
	docker cp $$(docker-compose ps -q redis):/data/dump.rdb ./backup-redis-$$(date +%Y%m%d_%H%M%S).rdb

restore-redis: ## Restaura backup do Redis (especifique BACKUP_FILE=arquivo.rdb)
	@if [ -z "$(BACKUP_FILE)" ]; then echo "Use: make restore-redis BACKUP_FILE=backup.rdb"; exit 1; fi
	docker-compose down redis
	docker cp $(BACKUP_FILE) $$(docker-compose ps -q redis):/data/dump.rdb
	docker-compose up -d redis

# Comandos de monitoramento
stats: ## Mostra estatísticas dos containers
	docker stats $$(docker-compose ps -q)

health: ## Verifica a saúde dos serviços
	@echo "Verificando saúde dos serviços..."
	@curl -f http://localhost:3000/api/stats || echo "❌ Aplicação não está respondendo"
	@docker-compose exec redis redis-cli ping || echo "❌ Redis não está respondendo"
	@echo "✅ Verificação concluída"
