# Script de setup para o wplace-proxy (Windows PowerShell)

Write-Host "🚀 Configurando wplace-proxy com Docker..." -ForegroundColor Green

# Verificar se Docker está instalado
try {
    docker --version | Out-Null
    Write-Host "✅ Docker detectado" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker não está instalado. Por favor, instale o Docker Desktop primeiro." -ForegroundColor Red
    exit 1
}

# Verificar se Docker Compose está instalado
try {
    docker-compose --version | Out-Null
    Write-Host "✅ Docker Compose detectado" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose não está instalado. Por favor, instale o Docker Compose primeiro." -ForegroundColor Red
    exit 1
}

# Criar arquivo .env se não existir
if (-not (Test-Path ".env")) {
    Write-Host "📝 Criando arquivo .env..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    
    # Gerar JWT_SECRET aleatório
    $bytes = New-Object Byte[] 32
    [Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($bytes)
    $JWT_SECRET = [Convert]::ToBase64String($bytes)
    
    (Get-Content ".env") -replace "your-super-secret-jwt-key-change-this-in-production", $JWT_SECRET | Set-Content ".env"
    Write-Host "🔐 JWT_SECRET gerado automaticamente" -ForegroundColor Green
} else {
    Write-Host "✅ Arquivo .env já existe" -ForegroundColor Green
}

# Criar diretórios necessários
Write-Host "📁 Criando diretórios necessários..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "artworks" | Out-Null
New-Item -ItemType Directory -Force -Path "tiles" | Out-Null
New-Item -ItemType Directory -Force -Path "ssl" | Out-Null

Write-Host "✅ Setup concluído!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Próximos passos:" -ForegroundColor Cyan
Write-Host ""
Write-Host "Para desenvolvimento:" -ForegroundColor White
Write-Host "  docker-compose up --build" -ForegroundColor Gray
Write-Host ""
Write-Host "Para produção:" -ForegroundColor White
Write-Host "  docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build" -ForegroundColor Gray
Write-Host ""
Write-Host "Para ver logs:" -ForegroundColor White
Write-Host "  docker-compose logs -f" -ForegroundColor Gray
Write-Host ""
Write-Host "🌐 Serviços disponíveis após iniciar:" -ForegroundColor Cyan
Write-Host "  - Aplicação principal: http://localhost:3000" -ForegroundColor White
Write-Host "  - Redis Commander (dev): http://localhost:8081" -ForegroundColor White
Write-Host "  - Nginx (prod): http://localhost:80" -ForegroundColor White
Write-Host ""
Write-Host "📖 Para mais informações, consulte README-Docker.md" -ForegroundColor Cyan
