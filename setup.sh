#!/bin/bash

# Script de setup para o wplace-proxy

set -e

echo "🚀 Configurando wplace-proxy com Docker..."

# Verificar se Docker está instalado
if ! command -v docker &> /dev/null; then
    echo "❌ Docker não está instalado. Por favor, instale o Docker primeiro."
    exit 1
fi

# Verificar se Docker Compose está instalado
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose não está instalado. Por favor, instale o Docker Compose primeiro."
    exit 1
fi

# Criar arquivo .env se não existir
if [ ! -f .env ]; then
    echo "📝 Criando arquivo .env..."
    cp .env.example .env
    
    # Gerar JWT_SECRET aleatório
    if command -v openssl &> /dev/null; then
        JWT_SECRET=$(openssl rand -base64 32)
        sed -i "s/your-super-secret-jwt-key-change-this-in-production/$JWT_SECRET/" .env
        echo "🔐 JWT_SECRET gerado automaticamente"
    else
        echo "⚠️  Por favor, edite o arquivo .env e mude o JWT_SECRET!"
    fi
else
    echo "✅ Arquivo .env já existe"
fi

# Criar diretórios necessários
echo "📁 Criando diretórios necessários..."
mkdir -p artworks
mkdir -p tiles
mkdir -p ssl

# Ajustar permissões
echo "🔧 Ajustando permissões..."
chmod 755 artworks tiles

# Verificar se Make está disponível
if command -v make &> /dev/null; then
    echo "🛠️  Make detectado. Você pode usar comandos como 'make dev' ou 'make prod'"
    USE_MAKE=true
else
    echo "⚠️  Make não está disponível. Use comandos docker-compose diretamente."
    USE_MAKE=false
fi

echo ""
echo "✅ Setup concluído!"
echo ""
echo "📋 Próximos passos:"
echo ""

if [ "$USE_MAKE" = true ]; then
    echo "Para desenvolvimento:"
    echo "  make dev"
    echo ""
    echo "Para produção:"
    echo "  make prod"
    echo ""
    echo "Outros comandos úteis:"
    echo "  make help     # Ver todos os comandos"
    echo "  make logs     # Ver logs"
    echo "  make health   # Verificar saúde dos serviços"
else
    echo "Para desenvolvimento:"
    echo "  docker-compose up --build"
    echo ""
    echo "Para produção:"
    echo "  docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build"
    echo ""
    echo "Para ver logs:"
    echo "  docker-compose logs -f"
fi

echo ""
echo "🌐 Serviços disponíveis após iniciar:"
echo "  - Aplicação principal: http://localhost:3000"
echo "  - Redis Commander (dev): http://localhost:8081"
echo "  - Nginx (prod): http://localhost:80"
echo ""
echo "📖 Para mais informações, consulte README-Docker.md"
